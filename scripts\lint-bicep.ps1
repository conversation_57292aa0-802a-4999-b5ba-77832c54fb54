# Bicep Linting Script for Local Development
# This script performs the same validation checks as the Azure DevOps pipeline

param(
    [string]$Path = ".",
    [switch]$SkipAdvancedValidation,
    [string]$Location = "East US 2"
)

Write-Host "=== Bicep Linting Script ===" -ForegroundColor Cyan
Write-Host "Path: $Path" -ForegroundColor Cyan
Write-Host "Skip Advanced Validation: $SkipAdvancedValidation" -ForegroundColor Cyan
Write-Host ""

# Check if Azure CLI is installed
try {
    $azVersion = az --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Azure CLI not found"
    }
    Write-Host "✅ Azure CLI is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
    exit 1
}

# Install/Update Bicep CLI
Write-Host "Installing/Updating Bicep CLI..." -ForegroundColor Yellow
try {
    az bicep install
    $bicepVersion = az bicep version
    Write-Host "✅ Bicep CLI: $bicepVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install/update Bicep CLI" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Find all .bicep files
Write-Host "=== BICEP FILE LINTING ===" -ForegroundColor Cyan
$bicepFiles = Get-ChildItem -Path $Path -Filter "*.bicep" -Recurse
$lintErrors = @()

if ($bicepFiles.Count -eq 0) {
    Write-Warning "No .bicep files found in path: $Path"
} else {
    Write-Host "Found $($bicepFiles.Count) Bicep files to lint:" -ForegroundColor Cyan
    foreach ($file in $bicepFiles) {
        $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\', '/')
        Write-Host "  - $relativePath" -ForegroundColor Cyan
    }
    Write-Host ""
    
    foreach ($file in $bicepFiles) {
        $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\', '/')
        Write-Host "Linting: $relativePath" -ForegroundColor Yellow
        
        try {
            # Run bicep build to validate syntax and check for errors
            $result = az bicep build --file $file.FullName --stdout 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ $($file.Name) - Linting passed" -ForegroundColor Green
            } else {
                Write-Host "❌ $($file.Name) - Linting failed" -ForegroundColor Red
                Write-Host "Error details:" -ForegroundColor Red
                Write-Host $result -ForegroundColor Red
                $lintErrors += "Bicep file: $relativePath"
            }
        } catch {
            Write-Host "❌ $($file.Name) - Exception during linting" -ForegroundColor Red
            Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
            $lintErrors += "Bicep file: $relativePath - Exception: $($_.Exception.Message)"
        }
        Write-Host ""
    }
}

# Find all .bicepparam files
Write-Host "=== BICEP PARAMETER FILE VALIDATION ===" -ForegroundColor Cyan
$paramFiles = Get-ChildItem -Path $Path -Filter "*.bicepparam" -Recurse
$validationErrors = @()

if ($paramFiles.Count -eq 0) {
    Write-Warning "No .bicepparam files found in path: $Path"
} else {
    Write-Host "Found $($paramFiles.Count) Bicep parameter files to validate:" -ForegroundColor Cyan
    foreach ($file in $paramFiles) {
        $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\', '/')
        Write-Host "  - $relativePath" -ForegroundColor Cyan
    }
    Write-Host ""
    
    foreach ($file in $paramFiles) {
        $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\', '/')
        Write-Host "Validating: $relativePath" -ForegroundColor Yellow
        
        try {
            # Check if the parameter file has valid syntax and references
            $content = Get-Content $file.FullName -Raw
            
            # Basic syntax validation - check for 'using' statement
            if ($content -notmatch "using\s+['\`"][^'\`"]+['\`"]") {
                Write-Host "❌ $($file.Name) - Missing or invalid 'using' statement" -ForegroundColor Red
                $validationErrors += "Parameter file: $relativePath - Missing or invalid 'using' statement"
                continue
            }
            
            # Extract the referenced Bicep file from 'using' statement
            $usingMatch = [regex]::Match($content, "using\s+['\`"]([^'\`"]+)['\`"]")
            if ($usingMatch.Success) {
                $referencedBicepFile = $usingMatch.Groups[1].Value
                $bicepFilePath = Join-Path (Split-Path $file.FullName -Parent) $referencedBicepFile
                
                if (-not (Test-Path $bicepFilePath)) {
                    Write-Host "❌ $($file.Name) - Referenced Bicep file not found: $referencedBicepFile" -ForegroundColor Red
                    $validationErrors += "Parameter file: $relativePath - Referenced Bicep file not found: $referencedBicepFile"
                } else {
                    Write-Host "✅ $($file.Name) - Parameter file validation passed" -ForegroundColor Green
                }
            } else {
                Write-Host "❌ $($file.Name) - Could not parse 'using' statement" -ForegroundColor Red
                $validationErrors += "Parameter file: $relativePath - Could not parse 'using' statement"
            }
        } catch {
            Write-Host "❌ $($file.Name) - Exception during validation" -ForegroundColor Red
            Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
            $validationErrors += "Parameter file: $relativePath - Exception: $($_.Exception.Message)"
        }
        Write-Host ""
    }
}

# Advanced validation with parameter files (optional)
if (-not $SkipAdvancedValidation -and $paramFiles.Count -gt 0) {
    Write-Host "=== ADVANCED BICEP VALIDATION ===" -ForegroundColor Cyan
    Write-Host "Note: This requires Azure login and appropriate permissions" -ForegroundColor Yellow
    Write-Host ""
    
    # Check if user is logged in to Azure
    try {
        $account = az account show 2>$null | ConvertFrom-Json
        if (-not $account) {
            throw "Not logged in"
        }
        Write-Host "✅ Logged in to Azure as: $($account.user.name)" -ForegroundColor Green
        Write-Host "Subscription: $($account.name) ($($account.id))" -ForegroundColor Green
        Write-Host ""
    } catch {
        Write-Host "❌ Not logged in to Azure. Skipping advanced validation." -ForegroundColor Yellow
        Write-Host "Run 'az login' to enable advanced validation." -ForegroundColor Yellow
        $SkipAdvancedValidation = $true
    }
    
    if (-not $SkipAdvancedValidation) {
        $advancedValidationErrors = @()
        
        foreach ($paramFile in $paramFiles) {
            $relativePath = $paramFile.FullName.Replace((Get-Location).Path, "").TrimStart('\', '/')
            Write-Host "Advanced validation for: $relativePath" -ForegroundColor Yellow
            
            try {
                # Use az deployment sub validate for subscription-scoped templates
                $result = az deployment sub validate `
                    --location $Location `
                    --template-file "main.bicep" `
                    --parameters $paramFile.FullName `
                    --only-show-errors 2>&1
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ $($paramFile.Name) - Advanced validation passed" -ForegroundColor Green
                } else {
                    Write-Host "❌ $($paramFile.Name) - Advanced validation failed" -ForegroundColor Red
                    Write-Host "Validation details:" -ForegroundColor Red
                    Write-Host $result -ForegroundColor Red
                    $advancedValidationErrors += "Parameter file: $relativePath"
                }
            } catch {
                Write-Host "❌ $($paramFile.Name) - Exception during advanced validation" -ForegroundColor Red
                Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
                $advancedValidationErrors += "Parameter file: $relativePath - Exception: $($_.Exception.Message)"
            }
            Write-Host ""
        }
        
        $validationErrors += $advancedValidationErrors
    }
}

# Summary
Write-Host "=== VALIDATION SUMMARY ===" -ForegroundColor Cyan
Write-Host "Bicep files processed: $($bicepFiles.Count)" -ForegroundColor Cyan
Write-Host "Parameter files processed: $($paramFiles.Count)" -ForegroundColor Cyan

$totalErrors = $lintErrors.Count + $validationErrors.Count
if ($totalErrors -gt 0) {
    Write-Host ""
    Write-Host "❌ Validation completed with $totalErrors error(s):" -ForegroundColor Red
    foreach ($error in ($lintErrors + $validationErrors)) {
        Write-Host "  - $error" -ForegroundColor Red
    }
    Write-Host ""
    Write-Host "Please fix the errors above before committing your changes." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host ""
    Write-Host "✅ All validations passed successfully!" -ForegroundColor Green
    Write-Host "Your Bicep files are ready for deployment." -ForegroundColor Green
    exit 0
}
