parameters:
- name: serviceConnection
  type: string
  default: 'DEV Infra Service Connection'

jobs:
- job: lint_bicep_files
  displayName: 'Lint and Validate Bicep Files'
  steps:
  - checkout: self
    displayName: 'Checkout Repository'

  - task: AzureCLI@2
    displayName: 'Install/Update Bicep CLI'
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
        Write-Host "Installing/Updating Bicep CLI..."
        az bicep install
        az bicep version
        Write-Host "Bicep CLI installation completed."

  - task: PowerShell@2
    displayName: 'Lint Bicep Files'
    inputs:
      targetType: 'inline'
      script: |
        Write-Host "Starting Bicep file linting..."
        
        # Find all .bicep files
        $bicepFiles = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Filter "*.bicep" -Recurse
        $lintErrors = @()
        
        if ($bicepFiles.Count -eq 0) {
          Write-Warning "No .bicep files found in the repository."
        } else {
          Write-Host "Found $($bicepFiles.Count) Bicep files to lint:"
          foreach ($file in $bicepFiles) {
            Write-Host "  - $($file.FullName)"
          }
          
          foreach ($file in $bicepFiles) {
            Write-Host ""
            Write-Host "Linting: $($file.FullName)" -ForegroundColor Yellow
            
            try {
              # Run bicep build to validate syntax and check for errors
              $result = az bicep build --file $file.FullName --stdout 2>&1
              
              if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ $($file.Name) - Linting passed" -ForegroundColor Green
              } else {
                Write-Host "❌ $($file.Name) - Linting failed" -ForegroundColor Red
                Write-Host "Error details:" -ForegroundColor Red
                Write-Host $result -ForegroundColor Red
                $lintErrors += "Bicep file: $($file.FullName)"
              }
            } catch {
              Write-Host "❌ $($file.Name) - Exception during linting" -ForegroundColor Red
              Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
              $lintErrors += "Bicep file: $($file.FullName) - Exception: $($_.Exception.Message)"
            }
          }
        }
        
        if ($lintErrors.Count -gt 0) {
          Write-Host ""
          Write-Host "❌ Bicep linting completed with errors:" -ForegroundColor Red
          foreach ($error in $lintErrors) {
            Write-Host "  - $error" -ForegroundColor Red
          }
          exit 1
        } else {
          Write-Host ""
          Write-Host "✅ All Bicep files passed linting!" -ForegroundColor Green
        }

  - task: PowerShell@2
    displayName: 'Validate Bicep Parameter Files'
    inputs:
      targetType: 'inline'
      script: |
        Write-Host "Starting Bicep parameter file validation..."
        
        # Find all .bicepparam files
        $paramFiles = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Filter "*.bicepparam" -Recurse
        $validationErrors = @()
        
        if ($paramFiles.Count -eq 0) {
          Write-Warning "No .bicepparam files found in the repository."
        } else {
          Write-Host "Found $($paramFiles.Count) Bicep parameter files to validate:"
          foreach ($file in $paramFiles) {
            Write-Host "  - $($file.FullName)"
          }
          
          foreach ($file in $paramFiles) {
            Write-Host ""
            Write-Host "Validating: $($file.FullName)" -ForegroundColor Yellow
            
            try {
              # Check if the parameter file has valid syntax and references
              $content = Get-Content $file.FullName -Raw
              
              # Basic syntax validation - check for 'using' statement
              if ($content -notmatch "using\s+['\`"][^'\`"]+['\`"]") {
                Write-Host "❌ $($file.Name) - Missing or invalid 'using' statement" -ForegroundColor Red
                $validationErrors += "Parameter file: $($file.FullName) - Missing or invalid 'using' statement"
                continue
              }
              
              # Extract the referenced Bicep file from 'using' statement
              $usingMatch = [regex]::Match($content, "using\s+['\`"]([^'\`"]+)['\`"]")
              if ($usingMatch.Success) {
                $referencedBicepFile = $usingMatch.Groups[1].Value
                $bicepFilePath = Join-Path (Split-Path $file.FullName -Parent) $referencedBicepFile
                
                if (-not (Test-Path $bicepFilePath)) {
                  Write-Host "❌ $($file.Name) - Referenced Bicep file not found: $referencedBicepFile" -ForegroundColor Red
                  $validationErrors += "Parameter file: $($file.FullName) - Referenced Bicep file not found: $referencedBicepFile"
                } else {
                  Write-Host "✅ $($file.Name) - Parameter file validation passed" -ForegroundColor Green
                }
              } else {
                Write-Host "❌ $($file.Name) - Could not parse 'using' statement" -ForegroundColor Red
                $validationErrors += "Parameter file: $($file.FullName) - Could not parse 'using' statement"
              }
            } catch {
              Write-Host "❌ $($file.Name) - Exception during validation" -ForegroundColor Red
              Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
              $validationErrors += "Parameter file: $($file.FullName) - Exception: $($_.Exception.Message)"
            }
          }
        }
        
        if ($validationErrors.Count -gt 0) {
          Write-Host ""
          Write-Host "❌ Bicep parameter file validation completed with errors:" -ForegroundColor Red
          foreach ($error in $validationErrors) {
            Write-Host "  - $error" -ForegroundColor Red
          }
          exit 1
        } else {
          Write-Host ""
          Write-Host "✅ All Bicep parameter files passed validation!" -ForegroundColor Green
        }

  - task: AzureCLI@2
    displayName: 'Advanced Bicep Validation with Parameter Files'
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'pscore'
      scriptLocation: 'inlineScript'
      inlineScript: |
        Write-Host "Starting advanced Bicep validation with parameter files..."
        
        # Find parameter files and their corresponding Bicep files
        $paramFiles = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Filter "*.bicepparam" -Recurse
        $advancedValidationErrors = @()
        
        foreach ($paramFile in $paramFiles) {
          Write-Host ""
          Write-Host "Advanced validation for: $($paramFile.FullName)" -ForegroundColor Yellow
          
          try {
            # Use az deployment sub validate for subscription-scoped templates
            # This validates the template with the parameter file
            $result = az deployment sub validate `
              --location "East US 2" `
              --template-file "main.bicep" `
              --parameters $paramFile.FullName `
              --only-show-errors 2>&1
            
            if ($LASTEXITCODE -eq 0) {
              Write-Host "✅ $($paramFile.Name) - Advanced validation passed" -ForegroundColor Green
            } else {
              Write-Host "❌ $($paramFile.Name) - Advanced validation failed" -ForegroundColor Red
              Write-Host "Validation details:" -ForegroundColor Red
              Write-Host $result -ForegroundColor Red
              $advancedValidationErrors += "Parameter file: $($paramFile.FullName)"
            }
          } catch {
            Write-Host "❌ $($paramFile.Name) - Exception during advanced validation" -ForegroundColor Red
            Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
            $advancedValidationErrors += "Parameter file: $($paramFile.FullName) - Exception: $($_.Exception.Message)"
          }
        }
        
        if ($advancedValidationErrors.Count -gt 0) {
          Write-Host ""
          Write-Host "❌ Advanced Bicep validation completed with errors:" -ForegroundColor Red
          foreach ($error in $advancedValidationErrors) {
            Write-Host "  - $error" -ForegroundColor Red
          }
          exit 1
        } else {
          Write-Host ""
          Write-Host "✅ All Bicep files passed advanced validation!" -ForegroundColor Green
        }

  - task: PowerShell@2
    displayName: 'Generate Bicep Validation Report'
    condition: always()
    inputs:
      targetType: 'inline'
      script: |
        Write-Host "Generating Bicep validation summary report..."
        
        $bicepFiles = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Filter "*.bicep" -Recurse
        $paramFiles = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Filter "*.bicepparam" -Recurse
        
        Write-Host ""
        Write-Host "=== BICEP VALIDATION SUMMARY ===" -ForegroundColor Cyan
        Write-Host "Bicep files found: $($bicepFiles.Count)" -ForegroundColor Cyan
        Write-Host "Parameter files found: $($paramFiles.Count)" -ForegroundColor Cyan
        Write-Host ""
        
        if ($bicepFiles.Count -gt 0) {
          Write-Host "Bicep files processed:" -ForegroundColor Cyan
          foreach ($file in $bicepFiles) {
            $relativePath = $file.FullName.Replace("$(Build.SourcesDirectory)", "").TrimStart('\', '/')
            Write-Host "  - $relativePath" -ForegroundColor Cyan
          }
        }
        
        if ($paramFiles.Count -gt 0) {
          Write-Host ""
          Write-Host "Parameter files processed:" -ForegroundColor Cyan
          foreach ($file in $paramFiles) {
            $relativePath = $file.FullName.Replace("$(Build.SourcesDirectory)", "").TrimStart('\', '/')
            Write-Host "  - $relativePath" -ForegroundColor Cyan
          }
        }
        
        Write-Host ""
        Write-Host "=== END VALIDATION SUMMARY ===" -ForegroundColor Cyan
