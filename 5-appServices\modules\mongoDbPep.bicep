metadata name = 'ALZ Bicep - PCP Application Infrastructure - App Services'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure App Services'

@sys.description('Main parameters')
param parLocation string
param parLocAbbrev string
param parCompanyPrefix string

param parMongoDBAcctName string
param parMongoSubId string
param parMongoResourceGroupName string
param parMongoIpConfigurations array = []

@sys.description('Template specific parameters')
param parSpokeName string
param parSpokeSubId string
param parHubSubId string
param parHubNetworkResourceGroupName string
param parPrivateDNSZoneNameMongo string = 'privatelink.mongo.cosmos.azure.com'

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varCustomAppsSubnetId = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'databaseSubnet')
var varVirtualNetworkIdToLink = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks', 'vnet-${parSpokeName}-${varLocAbbrev}')
var varPrivateDNSZoneMongo = resourceId(parHubSubId, parHubNetworkResourceGroupName, 'Microsoft.Network/privateDnsZones', parPrivateDNSZoneNameMongo)

@sys.description('get the MongoDB account')
resource resMongoDB 'Microsoft.DocumentDB/databaseAccounts@2025-04-15' existing = {
  name: parMongoDBAcctName
  scope: resourceGroup(parMongoSubId, parMongoResourceGroupName)
}

@sys.description('create private endpoint to mongoDb') 
resource resPrivateEndpointPrd 'Microsoft.Network/privateEndpoints@2024-07-01' = {
  name: 'pep-mongoDb-${substring(parMongoDBAcctName, 0, 4)}'
  location: parLocation
  properties: {
    ipConfigurations: parMongoIpConfigurations
    subnet: {
      id: varCustomAppsSubnetId
    }
    privateLinkServiceConnections: [
      {
        name: 'pep-${resMongoDB.name}'
        properties: {
          privateLinkServiceId: resMongoDB.id
          groupIds: [
            'MongoDB'
          ]
        }
      }
    ]
  }
}

@sys.description('Create private zone group for the private endpoint')
resource resPrivateDNSZoneGroupPrd 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2024-07-01' = {
  parent: resPrivateEndpointPrd
  name: 'dnsgroupname'
  properties: {
    privateDnsZoneConfigs: [
      {
        name: 'config1'
        properties: {
          privateDnsZoneId: varPrivateDNSZoneMongo
        }
      }
    ]
  }
}

@sys.description('Create a DNS link for the private endpoint')
module resMongoDNSLinkPrd '../../subModules/privateDNSLink/privateDNSLink.bicep' = {
  name: 'MongoDNSLink'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parSpoke: parSpokeName
    parPrivateDnsZoneName: parPrivateDNSZoneNameMongo
    parVirtualNetworkIdToLink: varVirtualNetworkIdToLink
  }
}
