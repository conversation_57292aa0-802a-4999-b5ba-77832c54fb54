param(
    [Parameter(Mandatory = $true)]
    [string]$Action,
    
    [Parameter(Mandatory = $true)]
    [string]$DataFactoryName,
    
    [Parameter(Mandatory = $true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory = $true)]
    [string]$SubscriptionId,
    
    [Parameter(Mandatory = $false)]
    [string]$EnabledTriggersList = ""
)

function Stop-AdfTriggers {
    param(
        [Parameter(Mandatory = $true)]
        [string]$DataFactoryName,
        
        [Parameter(Mandatory = $true)]
        [string]$ResourceGroupName,
        
        [Parameter(Mandatory = $true)]
        [string]$SubscriptionId
    )
    
    Write-Host "=== Starting ADF Trigger Stop Process ==="
    Write-Host "Data Factory: $DataFactoryName"
    Write-Host "Resource Group: $ResourceGroupName"
    Write-Host "Subscription: $SubscriptionId"
    
    # Set the subscription context
    az account set --subscription $SubscriptionId
    
    # Initialize variables
    $enabledTriggers = @()
    
    Write-Host "Checking if Data Factory '$DataFactoryName' exists in resource group '$ResourceGroupName'..."
    
    try {
        # Check if Data Factory exists
        $adfExists = az datafactory show --name $DataFactoryName --resource-group $ResourceGroupName 2>$null
        
        if ($adfExists) {
            Write-Host "Data Factory found. Checking for active triggers..."
            
            # Get all triggers
            $triggersJson = az datafactory trigger list --factory-name $DataFactoryName --resource-group $ResourceGroupName --output json
            
            if ($triggersJson) {
                $triggers = $triggersJson | ConvertFrom-Json
                
                foreach ($trigger in $triggers) {
                    Write-Host "Checking trigger: $($trigger.name)"
                    
                    # Get trigger details to check if it's enabled
                    $triggerDetailsJson = az datafactory trigger show --factory-name $DataFactoryName --resource-group $ResourceGroupName --name $trigger.name --output json
                    $triggerDetails = $triggerDetailsJson | ConvertFrom-Json
                    
                    if ($triggerDetails.properties.runtimeState -eq "Started") {
                        Write-Host "Trigger '$($trigger.name)' is enabled. Adding to list for later restoration."
                        $enabledTriggers += $trigger.name
                        
                        # Stop the trigger
                        Write-Host "Stopping trigger '$($trigger.name)'..."
                        az datafactory trigger stop --factory-name $DataFactoryName --resource-group $ResourceGroupName --name $trigger.name
                        
                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "Successfully stopped trigger '$($trigger.name)'"
                        } else {
                            Write-Warning "Failed to stop trigger '$($trigger.name)'"
                        }
                    } else {
                        Write-Host "Trigger '$($trigger.name)' is already stopped."
                    }
                }
                
                # Return enabled triggers list
                $enabledTriggersString = $enabledTriggers -join ","
                Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true]$enabledTriggersString"
                Write-Host "Stored enabled triggers for restoration: '$enabledTriggersString'"
                
                return $enabledTriggersString
            } else {
                Write-Host "No triggers found in Data Factory."
                Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true]"
                return ""
            }
        } else {
            Write-Host "Data Factory does not exist yet. This might be an initial deployment."
            Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true]"
            return ""
        }
    } catch {
        Write-Warning "Error occurred while managing triggers: $($_.Exception.Message)"
        Write-Host "##vso[task.setvariable variable=EnabledTriggers;isOutput=true]"
        return ""
    }
}

function Start-AdfTriggers {
    param(
        [Parameter(Mandatory = $true)]
        [string]$DataFactoryName,
        
        [Parameter(Mandatory = $true)]
        [string]$ResourceGroupName,
        
        [Parameter(Mandatory = $true)]
        [string]$SubscriptionId,
        
        [Parameter(Mandatory = $false)]
        [string]$EnabledTriggersList = ""
    )
    
    Write-Host "=== Starting ADF Trigger Restoration Process ==="
    Write-Host "Data Factory: $DataFactoryName"
    Write-Host "Resource Group: $ResourceGroupName"
    Write-Host "Subscription: $SubscriptionId"
    Write-Host "Enabled triggers to restore: '$EnabledTriggersList'"
    
    # Set the subscription context
    az account set --subscription $SubscriptionId
    
    if ($EnabledTriggersList -and $EnabledTriggersList.Trim() -ne "") {
        try {
            # Split the comma-separated string into an array
            $enabledTriggers = $EnabledTriggersList -split ","
            
            foreach ($triggerName in $enabledTriggers) {
                $triggerName = $triggerName.Trim()
                if ($triggerName -ne "") {
                    Write-Host "Re-enabling trigger '$triggerName'..."
                    
                    try {
                        az datafactory trigger start --factory-name $DataFactoryName --resource-group $ResourceGroupName --name $triggerName
                        
                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "Successfully re-enabled trigger '$triggerName'"
                        } else {
                            Write-Warning "Failed to re-enable trigger '$triggerName'"
                        }
                    } catch {
                        Write-Warning "Error re-enabling trigger '$triggerName': $($_.Exception.Message)"
                    }
                }
            }
        } catch {
            Write-Warning "Error processing enabled triggers list: $($_.Exception.Message)"
        }
    } else {
        Write-Host "No enabled triggers to restore."
    }
    
    Write-Host "=== ADF Trigger Restoration Process Complete ==="
}

# Main script execution
Write-Host "=== ADF Trigger Management Script Started ==="
Write-Host "Action: $Action"

switch ($Action.ToLower()) {
    "stop" {
        $result = Stop-AdfTriggers -DataFactoryName $DataFactoryName -ResourceGroupName $ResourceGroupName -SubscriptionId $SubscriptionId
        Write-Host "Stop operation completed. Result: '$result'"
    }
    "start" {
        Start-AdfTriggers -DataFactoryName $DataFactoryName -ResourceGroupName $ResourceGroupName -SubscriptionId $SubscriptionId -EnabledTriggersList $EnabledTriggersList
        Write-Host "Start operation completed."
    }
    default {
        Write-Error "Invalid action specified. Use 'stop' or 'start'."
        exit 1
    }
}

Write-Host "=== ADF Trigger Management Script Completed ==="
