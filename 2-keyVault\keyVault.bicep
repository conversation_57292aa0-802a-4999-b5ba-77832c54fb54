metadata name = '<PERSON>Z Bicep - PCP Application Infrastructure - Key Vault'
metadata description = 'ALZ Bicep Module used to set up PCP Application Infrastructure Key Vault'

type lockType = {
  @description('Optional. Specify the name of lock.')
  name: string?

  @description('Optional. The lock settings of the service.')
  kind: ('CanNotDelete' | 'ReadOnly' | 'None')

  @description('Optional. Notes about this lock.')
  notes: string?
}
param parGlobalResourceLock lockType
param parLocation string
param parLocAbbrev string
param parCompanyPrefix string
param parEnvironment string
param parKvTags object = {}
param parSpokeName string
param parAppName string
param parSpokeSubId string
param parHubSubId string
param parHubNetworkResourceGroupName string
param parSpokeNetworkAddressOctets string
param parTenantId string
param parPrivateDNSZoneNameKeyvault string

@sys.description('Secrets')
@secure()
#disable-next-line no-unused-params
param parMgtClientSecretPrd string
@secure()
#disable-next-line no-unused-params
param parMgtClientSecretAcc string
@secure()
#disable-next-line no-unused-params
param parCltClientSecretPrd string
@secure()
#disable-next-line no-unused-params
param parCltClientSecretAcc string
@secure()
#disable-next-line no-unused-params
param parApiClientSecretPrd string
@secure()
#disable-next-line no-unused-params
param parApiClientSecretAcc string
@secure()
#disable-next-line no-unused-params
param parMgtSessionSecretPrd string
@secure()
#disable-next-line no-unused-params
param parMgtSessionSecretAcc string
@secure()
#disable-next-line no-unused-params
param parCltClientSecretExternalIdPrd string
@secure()
#disable-next-line no-unused-params
param parCltClientSecretExternalIdAcc string
@secure()
#disable-next-line no-unused-params
param parApiSmtpPasswordAcc string
@secure()
#disable-next-line no-unused-params
param parApiSmtpPasswordPrd string
@secure()
#disable-next-line no-unused-params
param parMongoDbConnStrSecret string
@secure()
#disable-next-line no-unused-params
param parSqlMiPcpStagingDbSecretAcc string
@secure()
#disable-next-line no-unused-params
param parSqlMiPcpStagingDbSecretPrd string

param parAppkeyVaultLock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure Key Vault.'
}
param parKeyVaultPrivateEndpoint01Lock lockType = {
  kind: 'CanNotDelete'
  notes: 'This lock was created by the ALZ Bicep PCP Application Infrastructure Key Vault.'
}

var varLocAbbrev = parLocAbbrev == 'eus2' ? 'eastus2' : parLocAbbrev

var varCustomAppsSubnetId = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks/subnets', 'vnet-${parSpokeName}-${varLocAbbrev}', 'keyvaultSubnet')
var varVirtualNetworkIdToLink = resourceId(parSpokeSubId, 'rg-${parCompanyPrefix}-${parSpokeName}-networking-${parLocAbbrev}', 'Microsoft.Network/virtualNetworks', 'vnet-${parSpokeName}-${varLocAbbrev}')
var varPrivateDNSZoneKeyvault = resourceId(parHubSubId, parHubNetworkResourceGroupName, 'Microsoft.Network/privateDnsZones', 'privatelink.vaultcore.azure.net')

resource resAppkeyVault 'Microsoft.KeyVault/vaults@2024-11-01' = {
  name: 'kv-${take(parAppName, 9)}-${parEnvironment}-${parLocAbbrev}'
  location: parLocation
  tags: union(parKvTags, {
    workloadtype: 'security'
    environment: parEnvironment
  })
  properties: {
    enabledForDeployment: false
    enabledForDiskEncryption: false
    enabledForTemplateDeployment: false
    enablePurgeProtection: true
    enableRbacAuthorization: true
    enableSoftDelete: true
    networkAcls: {
      bypass: 'None'
      defaultAction: 'deny'
    }
    publicNetworkAccess: 'Disabled'
    sku: {
      family: 'A'
      name: 'standard'
    }
    softDeleteRetentionInDays: 90
    tenantId: parTenantId
  }
}

@description('Create a resource lock for the Key Vault if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resAppkeyVaultLock 'Microsoft.Authorization/locks@2020-05-01' = if (parAppkeyVaultLock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resAppkeyVault
  name: parAppkeyVaultLock.?name ?? '${resAppkeyVault.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parAppkeyVaultLock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parAppkeyVaultLock.?notes
  }
}

resource resKeyVaultPrivateEndpoint01 'Microsoft.Network/privateEndpoints@2024-01-01' = {
  name: 'pep-${resAppkeyVault.name}'
  location: parLocation
  tags: union(parKvTags, {
    workloadtype: 'networking'
    environment: parEnvironment
  })
  properties: {
    ipConfigurations: [
      {
        name: 'ipconfig1'
        properties: {
          groupId: 'vault'
          memberName: 'default'
          privateIPAddress: '${parSpokeNetworkAddressOctets}.5.4'
        }
      }
    ]
    subnet: {
      id: varCustomAppsSubnetId
    }
    privateLinkServiceConnections: [
      {
        name: 'pep-${resAppkeyVault.name}'
        properties: {
          privateLinkServiceId: resAppkeyVault.id
          groupIds: [
            'vault'
          ]
        }
      }
    ]
  }
}

@description('Create a resource lock for the Storage accounts private endpoint if parGlobalResourceLock.kind != "None" or if parAutomationAccountLock.kind != "None"')
resource resSaPrivateEndpoint01Lock 'Microsoft.Authorization/locks@2020-05-01' = if (parKeyVaultPrivateEndpoint01Lock.kind != 'None' || parGlobalResourceLock.kind != 'None') {
  scope: resKeyVaultPrivateEndpoint01
  name: parKeyVaultPrivateEndpoint01Lock.?name ?? '${resKeyVaultPrivateEndpoint01.name}-lock'
  properties: {
    level: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.kind : parKeyVaultPrivateEndpoint01Lock.kind
    notes: (parGlobalResourceLock.kind != 'None') ? parGlobalResourceLock.?notes : parKeyVaultPrivateEndpoint01Lock.?notes
  }
}

resource resPrivateDNSZoneGroup 'Microsoft.Network/privateEndpoints/privateDnsZoneGroups@2024-01-01' = {
  parent: resKeyVaultPrivateEndpoint01
  name: 'dnsgroupname'
  properties: {
    privateDnsZoneConfigs: [
      {
        name: 'config1'
        properties: {
          privateDnsZoneId: varPrivateDNSZoneKeyvault
        }
      }
    ]
  }
}

module resKvDNSLink '../subModules/privateDNSLink/privateDNSLink.bicep' = {
  name: 'kvDNSLink'
  scope: resourceGroup(parHubSubId, parHubNetworkResourceGroupName)
  params: {
    parSpoke: parSpokeName
    parPrivateDnsZoneName: parPrivateDNSZoneNameKeyvault
    parVirtualNetworkIdToLink: varVirtualNetworkIdToLink
  }
}

resource resKeyVaultSecretMgtClientSecretPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-mgt-client-secret-prd'
  properties: {
    value: parMgtClientSecretPrd
  }
}

resource resKeyVaultSecretMgtClientSecretAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-mgt-client-secret-acc'
  properties: {
    value: parMgtClientSecretAcc
  }
}

resource resKeyVaultSecretCltClientSecretPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-clt-client-secret-prd'
  properties: {
    value: parCltClientSecretPrd
  }
}

resource resKeyVaultSecretCltClientSecretAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-clt-client-secret-acc'
  properties: {
    value: parCltClientSecretAcc
  }
}

resource resKeyVaultSecretApiClientSecretPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-api-client-secret-prd'
  properties: {
    value: parApiClientSecretPrd
  }
}

resource resKeyVaultSecretApiClientSecretAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-api-client-secret-acc'
  properties: {
    value: parApiClientSecretAcc
  }
}

resource resKeyVaultSecretMgtSessionSecretPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-mgt-session-secret-prd'
  properties: {
    value: parMgtSessionSecretPrd
  }
}

resource resKeyVaultSecretMgtSessionSecretAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-mgt-session-secret-acc'
  properties: {
    value: parMgtSessionSecretAcc
  }
}

resource resKeyVaultSecretExternalIdPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-clt-client-secret-externalid-prd'
  properties: {
    value: parCltClientSecretExternalIdPrd
  }
}

resource resKeyVaultSecretExternalIdAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-clt-client-secret-externalid-acc'
  properties: {
    value: parCltClientSecretExternalIdAcc
  }
}

resource resKeyVaultSecretApiSmtpPasswordPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-api-smtp-password-prd'
  properties: {
    value: parApiSmtpPasswordPrd
  }
}

resource resKeyVaultSecretApiSmtpPasswordAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-api-smtp-password-acc'
  properties: {
    value: parApiSmtpPasswordAcc
  }
}

resource resKeyVaultSecretSqlMiPcpStagingDbSecretPrd 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-vp-sqlmi-db-password-Prd'
  properties: {
    value: parSqlMiPcpStagingDbSecretPrd
  }
}

resource resKeyVaultSecretSqlMiPcpStagingDbSecretAcc 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-vp-sqlmi-db-password-Acc'
  properties: {
    value: parSqlMiPcpStagingDbSecretAcc
  }
}

resource resKeyVaultSecretApiMongoDbConnStr 'Microsoft.KeyVault/vaults/secrets@2024-11-01' = {
  parent: resAppkeyVault
  name: 'secret-api-mongodb-connectionstring'
  properties: {
    value: parMongoDbConnStrSecret
  }
}
