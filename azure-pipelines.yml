trigger: none

name: PCP_Infra_$(Date:yyyyMMdd)$(Rev:.r)

pool:
  name: TT PCP - WindowsAgents prd

stages:
# Bicep Linting and Validation Stage
- stage: bicep_validation
  displayName: 'Bicep Linting and Validation'
  jobs:
  - template: bicep-linting-template.yml
    parameters:
      serviceConnection: 'DEV Infra Service Connection'

# Deploy to Development Environment
- template: deploy-environment-template.yml
  parameters:
    environment: dev
    azureDevopsEnvironment: Development
    serviceConnection: DEV Infra Service Connection
    dependsOn: [bicep_validation]

# Deploy to PRD2 Environment
- template: deploy-environment-template.yml
  parameters:
    environment: prd2
    azureDevopsEnvironment: Production 2
    serviceConnection: PRD Infra Service Connection
    dependsOn: [deploy_dev]

# Deploy to Production Environment
- template: deploy-environment-template.yml
  parameters:
    environment: prd
    azureDevopsEnvironment: Production
    serviceConnection: PRD Infra Service Connection
    dependsOn: [deploy_dev]
