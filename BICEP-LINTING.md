# Bicep Linting and Validation

This document describes the Bicep linting and validation process implemented in the Azure DevOps pipeline.

## Overview

The pipeline now includes a comprehensive Bicep linting and validation stage that runs before any deployment stages. This ensures that all Bicep files and Bicep parameter files are valid and properly configured before attempting deployment.

## Validation Stages

### 1. Bicep CLI Installation
- Installs or updates the latest version of the Bicep CLI
- Ensures compatibility with the latest Bicep features and syntax

### 2. Bicep File Linting
- Discovers all `*.bicep` files in the repository recursively
- Validates syntax and structure using `az bicep build`
- Checks for:
  - Syntax errors
  - Type mismatches
  - Invalid resource references
  - Best practice violations

### 3. Bicep Parameter File Validation
- Discovers all `*.bicepparam` files in the repository recursively
- Validates parameter file structure and syntax
- Checks for:
  - Valid `using` statement syntax
  - Referenced Bicep file existence
  - Parameter file format compliance

### 4. Advanced Validation with Parameter Files
- Performs Azure Resource Manager template validation
- Uses `az deployment sub validate` for subscription-scoped templates
- Validates the complete deployment scenario including:
  - Parameter compatibility with template
  - Resource dependencies
  - Azure policy compliance
  - Resource provider availability

### 5. Validation Report Generation
- Generates a summary report of all validation activities
- Lists all processed files
- Provides validation statistics

## Files Involved

### Pipeline Files
- `azure-pipelines.yml` - Main pipeline with bicep validation stage
- `bicep-linting-template.yml` - Reusable template for Bicep validation
- `deploy-environment-template.yml` - Environment deployment template

### Bicep Files
- `main.bicep` - Main infrastructure template
- `main.dev.bicepparam` - Development environment parameters
- `main.prd.bicepparam` - Production environment parameters
- `main.prd2.bicepparam` - Production 2 environment parameters
- Various module files in subdirectories

## Pipeline Flow

```
┌─────────────────────────┐
│   Bicep Validation      │
│   - Install Bicep CLI   │
│   - Lint .bicep files   │
│   - Validate .bicepparam│
│   - Advanced validation │
│   - Generate report     │
└─────────────────────────┘
            │
            ▼
┌─────────────────────────┐
│   Deploy Development    │
│   - Stop ADF Triggers   │
│   - Deploy Infrastructure│
│   - Start ADF Triggers  │
└─────────────────────────┘
            │
            ▼
┌─────────────────────────┐
│   Deploy PRD2          │
│   (Parallel with PRD)   │
└─────────────────────────┘
            │
            ▼
┌─────────────────────────┐
│   Deploy Production     │
│   (Parallel with PRD2)  │
└─────────────────────────┘
```

## Error Handling

The validation stage will fail the entire pipeline if any of the following issues are detected:

### Bicep File Issues
- Syntax errors in Bicep files
- Invalid resource type references
- Missing required parameters
- Type mismatches
- Circular dependencies

### Parameter File Issues
- Missing or invalid `using` statement
- Referenced Bicep file not found
- Invalid parameter syntax
- Parameter type mismatches

### Advanced Validation Issues
- Azure policy violations
- Resource provider not available in target region
- Insufficient permissions for deployment
- Resource naming conflicts
- Dependency resolution failures

## Benefits

1. **Early Error Detection**: Catches issues before deployment attempts
2. **Faster Feedback**: Developers get immediate feedback on Bicep code quality
3. **Consistent Quality**: Ensures all Bicep code meets standards before deployment
4. **Reduced Deployment Failures**: Prevents failed deployments due to template issues
5. **Cost Savings**: Avoids partial deployments that may incur costs

## Local Development

### Using the Provided Scripts

The repository includes scripts that replicate the pipeline validation locally:

#### PowerShell Script (Recommended)
```powershell
# Run from repository root
.\scripts\lint-bicep.ps1

# Run with custom path
.\scripts\lint-bicep.ps1 -Path "path/to/bicep/files"

# Skip advanced validation (faster, no Azure login required)
.\scripts\lint-bicep.ps1 -SkipAdvancedValidation

# Use different Azure region for validation
.\scripts\lint-bicep.ps1 -Location "West Europe"
```

#### Windows Batch File
```cmd
# Run from repository root
scripts\lint-bicep.cmd

# Pass parameters (same as PowerShell script)
scripts\lint-bicep.cmd -SkipAdvancedValidation
```

### Manual Validation Commands

Developers can also run validation manually using Azure CLI:

```bash
# Install Bicep CLI
az bicep install

# Lint a specific Bicep file
az bicep build --file main.bicep

# Validate deployment (requires Azure login)
az deployment sub validate \
  --location "East US 2" \
  --template-file "main.bicep" \
  --parameters "main.dev.bicepparam"
```

### Prerequisites for Local Development

1. **Azure CLI**: Install from https://docs.microsoft.com/en-us/cli/azure/install-azure-cli
2. **PowerShell**: Windows PowerShell 5.1+ or PowerShell Core 6.0+
3. **Azure Login**: For advanced validation, run `az login` first

## Troubleshooting

### Common Issues

1. **Bicep CLI Not Found**
   - Solution: Ensure Azure CLI is installed and updated

2. **Permission Denied During Validation**
   - Solution: Check service connection permissions for the target subscription

3. **Parameter File Reference Errors**
   - Solution: Verify the `using` statement points to the correct Bicep file path

4. **Advanced Validation Timeouts**
   - Solution: Check Azure service availability and network connectivity

### Getting Help

- Check the pipeline logs for detailed error messages
- Review the validation report generated at the end of the stage
- Consult the Azure Bicep documentation for syntax and best practices
- Contact the DevOps team for pipeline-specific issues

## Future Enhancements

Potential improvements to consider:

1. **Bicep Best Practices Analysis**: Add custom rules for organization-specific standards
2. **Security Scanning**: Integrate security analysis tools for Bicep templates
3. **Performance Analysis**: Add checks for resource sizing and cost optimization
4. **Documentation Generation**: Auto-generate documentation from Bicep files
5. **Integration Testing**: Add tests for deployed resources functionality
