@echo off
REM Bicep Linting Script for Local Development (Windows Batch)
REM This script runs the PowerShell linting script

echo === Bicep Linting Script ===
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set SCRIPT_DIR=%~dp0

REM Run the PowerShell script
echo Running Bicep linting...
echo.
powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%lint-bicep.ps1" %*

REM Check the exit code from PowerShell
if %errorlevel% neq 0 (
    echo.
    echo Linting failed. Please check the errors above.
    pause
    exit /b %errorlevel%
) else (
    echo.
    echo Linting completed successfully!
    pause
    exit /b 0
)
